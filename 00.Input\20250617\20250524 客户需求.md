消防指令システムで新規コンテンツ追加の依頼がありました。
既存コンテンツである車両コンテンツ（最大32行表示）
の拡張版というイメージで、
文字数、表示情報は全く共通で一画面で表示する行数が32⇒50行に増えたものとなります。
既存はそのままで新規のコンテンツとなるため、ID等は新しい番号が振られる形となります。

１．リターンコードについて
コンテンツ情報更新及び制御情報顧信のリターンコード(result)は、全て（0:成功　-1:失敗）としていますが、これを配列にしてソース番号などを付加するのは難しいでしょうか
また、対応可能の場合の工数をご提示いただけますでしょうか

２．エラー表示について
コンテンツ情報更新時に、パラメータのバリデーションはOKでその後、何らかの障害で表示ができなかった場合にエラー画面が表示されるような仕様となっていますでしょうか



１．リターンコードについて
→難しいではないが、修正範囲は広いので、2週間ぐらい

２．エラー表示について
エラー画面表示がありますが、どのエラーが発生して、表示するのは、あまり設計できてない。
そもそも前回、このあたりの設計がないので、今回にこのあたりを整理してきちんと対応したほうが良いと思います。
対応する場合、2-3週間がかかりそうです。